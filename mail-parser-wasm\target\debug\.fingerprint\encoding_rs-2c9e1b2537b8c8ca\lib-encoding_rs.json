{"rustc": 8713626761367032038, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 5079274106931611199, "profile": 10243973527296709326, "path": 1563166383658566647, "deps": [[5682297152023424035, "cfg_if", false, 16022683977646009264]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\encoding_rs-2c9e1b2537b8c8ca\\dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "metadata": 10075669053249481654, "config": 2202906307356721367, "compile_kind": 0}