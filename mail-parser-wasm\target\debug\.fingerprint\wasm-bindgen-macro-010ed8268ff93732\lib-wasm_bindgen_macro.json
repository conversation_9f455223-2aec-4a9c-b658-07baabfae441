{"rustc": 8713626761367032038, "features": "[]", "declared_features": "[\"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 10757748937472569276, "profile": 12174896561884874532, "path": 14679482479013842004, "deps": [[10934777256049406769, "wasm_bindgen_macro_support", false, 2437981386009806085], [17525013869477438691, "quote", false, 8238036605326618435]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wasm-bindgen-macro-010ed8268ff93732\\dep-lib-wasm_bindgen_macro", "checksum": false}}], "rustflags": [], "metadata": 17576434215897489395, "config": 2202906307356721367, "compile_kind": 0}