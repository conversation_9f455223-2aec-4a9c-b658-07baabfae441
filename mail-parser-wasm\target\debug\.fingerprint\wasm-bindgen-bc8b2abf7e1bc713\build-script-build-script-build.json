{"rustc": 8713626761367032038, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 9652763411108993936, "profile": 2859854238780875620, "path": 7749682557102362072, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wasm-bindgen-bc8b2abf7e1bc713\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "metadata": 1893760814388251298, "config": 2202906307356721367, "compile_kind": 0}