{"rustc": 8713626761367032038, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 18215736919814824467, "profile": 12174896561884874532, "path": 3641493092092967829, "deps": [[14125489736606713511, "syn", false, 14720280405171085547], [15711452346824113666, "wasm_bindgen_backend", false, 17362758086003550992], [15771991779085741627, "wasm_bindgen_shared", false, 16186982535546659453], [17525013869477438691, "quote", false, 8238036605326618435], [18036439996138669183, "proc_macro2", false, 13358315204244837920]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wasm-bindgen-macro-support-a52161e199231684\\dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "metadata": 3850881919609461430, "config": 2202906307356721367, "compile_kind": 0}