{"rustc": 8713626761367032038, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 13956961843040149721, "profile": 1043327292326101077, "path": 10960876887773704249, "deps": [[1668188761395632249, "build_script_build", false, 649962372636672404], [2988349922222251039, "wasm_bindgen_macro", false, 6674003317070081107], [3882957448493667176, "rustversion", false, 17795303592485213333], [5682297152023424035, "cfg_if", false, 16022683977646009264], [8244776183334334055, "once_cell", false, 385293305385905934]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wasm-bindgen-99404bcc32e92c3b\\dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": [], "metadata": 1893760814388251298, "config": 2202906307356721367, "compile_kind": 0}